<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON> khan</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" href="assets/images/me.jpg">
</head>

<body>
    <!-- Navbar SECTION -->
    <header class="header">
        <a href="#" class="logo">A.Owais khan</a>

        <!-- <i class="bx bx-menu" id="menu-icon">a</i> -->
        <img src="assets/SVG/hamburger-menu-icon.svg" id="menu-icon"></img>

        <nav class="navbar">
            <a href="#home" class="active">Home</a>
            <a href="assets/pages/documentation.html">Documentation</a>
            <a href="assets/pages/about.html">About</a>
            <a href="assets/pages/projects.html">Projects</a>
            <a href="assets/pages/courses.html">Courses</a>
            <a href="assets/pages/contact.html">Contact</a>

        </nav>
    </header>

    <!-- Home Section -->

    <section id="home" class="home">
        <div class="home-img">
            <img src="assets/images/me.jpg" alt="Profile Image">
        </div>
        <div class="home-content">
            <h3>Hello, Users</h3>
            <h1>A.Owais khan</h1>
            <h3>And I'm Creating <span class="home-mt"></span></h3>
            <p>If you wont to belt any web/apps designs , website , python project then contact me <span><a
                        href="#"><EMAIL></a></span> on this Gmail.</p>
            <div class="social-media">
                <a href="#"><i class="bx bxl-youtube"></i></a>
                <a href="https://github.com/ahmad010709"><i class="bx bxl-github"></i></a>
                <a href="#"><i class="bx bxl-instagram"></i></a>
                <a href="#"><i class="bx bxl-tiktok"></i></a>
            </div>
            <a href="assets/pages/about.html" class="p-btn p-btn-primary">Open CV</a>
        </div>


    </section>

    <!-- Project Section -->


    <div class="p-container">
        <header class="projects-header">
            <h1>My Projects</h1>
        </header>

        <div class="projects-grid">

            <!-- Portfolio Website -->
            <div class="project-card">
                <div class="project-image">
                    <img src="assets/images/web.png">
                </div>
                <div class="project-content">
                    <h3>Portfolio Website</h3>
                    <p>You can download thia webapp</p>
                    <div class="project-links">
                        <a href="assets/apk/owais.apk" download class="p-btn p-btn-dark">
                            <i class="fa-solid fa-download"></i> Install
                        </a>
                        <a href="https://github.com/ahmad010709/Web-Project" class="p-btn p-btn-dark">
                            <i class="fab fa-github"></i> GitHub
                        </a>
                       <a href="https://ahmad010709.github.io/Web-Project" class="p-btn p-btn-primary">
                            <i class="fas fa-external-link-alt"></i> Live Preview
                        </a>
                    </div>
                </div>
            </div>


            <!-- Chess Game -->
            <div class="project-card">
                <div class="project-image">
                    <img src="assets/images/chess game.png">
                </div>
                <div class="project-content">
                    <h3>Chess Game</h3>
                    <p>You can play this game with robot and fiends</p>
                    <div class="project-links">
                        <a href="assets/apk/chess.apk" download class="p-btn p-btn-dark">
                            <i download class="fa-solid fa-download"></i> Install
                        </a>
                        <a href="https://github.com/ahmad010709/ChessGame" class="p-btn p-btn-dark">
                            <i class="fab fa-github"></i> GitHub
                        </a>
                        <a href="https://chess-game-beryl-seven.vercel.app" class="p-btn p-btn-primary">
                            <i class="fas fa-external-link-alt"></i> Live Preview
                        </a>
                    </div>
                </div>
            </div>

            <!-- Cosmetic Ai  -->
            <div class="project-card">
                <div class="project-image">
                    <img src="assets/images/cosmetic ai.png">
                </div>
                <div class="project-content">
                    <h3>Cosmetic Ai</h3>
                    <p>You can upload name or photo of the cosmetic . this app give you info about that product.</p>
                    <div class="project-links">
                        <a href="assets/apk/cosmetic.apk" download class="p-btn p-btn-dark">
                            <i class="fa-solid fa-download"></i> Install
                        </a>
                        <a href="https://github.com/ahmad010709/cosmetic-ai-insight" class="p-btn p-btn-dark">
                            <i class="fab fa-github"></i> GitHub
                        </a>
                        <a href="https://cosmetic-ai-insight.vercel.app" class="p-btn p-btn-primary">
                            <i class="fas fa-external-link-alt"></i> Live Preview
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Courses Section -->


    <div class="p-container">
        <header class="projects-header">
            <h1>Courses</h1>
        </header>

        <div class="projects-grid">
            <!--  1 -->
            <div class="project-card" onclick="window.location.href='assets/pages/courses-python.html'">
                <div class="project-image">
                    <img src="assets/images/p.jpg">
                </div>
                <div class="project-content">
                    <h3>Python</h3>
                    <p>This courses help you to learn Creating app and game</p>
                    <div class="project-links">
                        <a href="#" class="p-btn p-btn-dark">
                            <i class="fa-solid fa-play"></i>Watch Now
                        </a>
                    </div>
                </div>
            </div>
            <!-- 2 -->
            <div class="project-card" onclick="window.location.href='assets/pages/courses-web.html'">
                <div class="project-image">
                    <img src="assets/images/w.jpg">
                </div>
                <div class="project-content">
                    <h3>Creating Website</h3>
                    <p>with html , css , js </p>
                    <div class="project-links">
                        <a href="#" class="p-btn p-btn-dark">
                            <i class="fa-solid fa-play"></i>Watch Now
                        </a>
                    </div>
                </div>
            </div>
            <!-- 3 -->
            <div class="project-card" onclick="window.location.href='assets/pages/courses-mysql.html'">
                <div class="project-image">
                    <img src="assets/images/s.jpg">
                </div>
                <div class="project-content">
                    <h3>MySQL Database</h3>
                    <p>how to createing database</p>
                    <div class="project-links">
                        <a href="#" class="p-btn p-btn-dark">
                            <i class="fa-solid fa-play"></i>Watch Now
                        </a>
                    </div>
                </div>
            </div>

            <!-- 4 -->
            <div class="project-card" onclick="window.location.href='assets/pages/courses-game-hacking.html'">
                <div class="project-image">
                    <img src="assets/images/g.jpg">
                </div>
                <div class="project-content">
                    <h3>Game Hacking</h3>
                    <p>hwo to hack games full coruses with projects</p>
                    <div class="project-links">
                        <a href="#" class="p-btn p-btn-dark">
                            <i class="fa-solid fa-play"></i>Watch Now
                        </a>
                    </div>
                </div>
            </div>

            <!-- 5 -->
            <div class="project-card" onclick="window.location.href='assets/pages/courses-unreal-engine.html'">
                <div class="project-image">
                    <img src="assets/images/u.jpg">
                </div>
                <div class="project-content">
                    <h3>Unreal Engine 5</h3>
                    <p>Creating 3d game like Pubg moblier also pubg created inside Unreal Engine</p>
                    <div class="project-links">
                        <a href="#" class="p-btn p-btn-dark">
                            <i class="fa-solid fa-play"></i>Watch Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- About Section -->
    <section class="about">

        <div class="about-content">
            <h1 class="heading">Ahmad owais khan</h1>
            <img src="assets/images/me.jpg" alt="">

            <h3>contact</h3>
            <p>address: Haryan Kot, tehsil Dargai district Malakand,KPK, Pakistan<br> Phone: +92 3271697695 <br> Email:
                <EMAIL></p>
            <h3>languages</h3>
            <p>english(read, write & speak) <br> urdu (speak) <br> pashto (speak)</p>

        </div>

        <div class="about-content">
            <h1 class="heading">(a sample cv)</h1>
            <h3>summary</h3>
            <p>senior web developer specializing in front end development. experienced with all stages of the
                development cycle for dynamic web projects. well versed in numerous programming languages including
                HTML5 , CSS , PHP OOP , JavaScript, MYSQL strong background in project management and customer
                relations.</p>
          <h3>education</h3>
            <p>Digres : Deploma information technology - 2025</p>
            <h3>Skils</h3>
            <p>Online Certificate: Developing Web App Game , Genrative Ai , Unreal Engine Game Development</p>
            
            <h3>experience</h3>
            <p>developing start 09-11-2018
                <br>
                learn web design, Malakand
                <br>
                cooperate with designers to create clean interfaces and simple, intuitive interactions and
                experiences.
                <br>
                develop project concepts and maintaion optimal workflow.
                <br>
                work with senior developer to manage large, complex design projects for corporate clients.
                complete detailed programming and development tasks for front end public and internal websites as
                well as challenging back end server code.
                <br>
                carry out quality assurance tests to discover errors and optimize usability.
            </p>
            
        </div>

        </div>

    </section>

    <!-- Footer Section -->

    <footer class="footer">
        <div class="social">
            <a href="#"><i class="bx bxl-youtube"></i></a>
            <a href="https://github.com/ahmad010709"><i class="bx bxl-github"></i></a>
            <a href="#"><i class="bx bxl-instagram"></i></a>
            <a href="#"><i class="bx bxl-tiktok"></i></a>
        </div>

        <p class="copyright">
            © A.Owais khan - Created . Scourse code is avilable on Project page
        </p>
    </footer>
    <!-- ads social bar link -->
    <script type='text/javascript'
        src='//pl27428749.profitableratecpm.com/1b/00/e9/1b00e9b611843a363e841a02a1648720.js'></script>
        <!-- end -->
    <script src="https://cdn.jsdelivr.net/npm/typed.js@2.0.12"></script>
    <script src="assets/js/script.js"></script>


</body>

</html>